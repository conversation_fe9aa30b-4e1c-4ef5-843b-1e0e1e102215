

"use client";

import { cn } from "@/lib/utils";
import {
  CreditCardIcon,
  Settings,
  ShoppingBag,
  TrendingDown,
  TrendingUp,
  TruckIcon,
  UserIcon,
  UsersRound,
  Warehouse,
  ChevronDown,
  ChevronRight,
  BarChart3,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { GoHome, GoHomeFill } from "react-icons/go";

const generalRoutes = [
  {
    label: "Dashboard",
    href: "/",
    Icon: GoHome,
    activeIcon: GoHomeFill,
  },
   {
    label: "Inventory",
    href: "/inventories",
    Icon: Warehouse,
    activeIcon: Warehouse,
  },
  {
    label: "Sales",
    href: `/sales`,
    Icon: TrendingUp,
    activeIcon: TrendingUp,
  },
  {
    label: "Purchases",
    href: `/purchases`,
    Icon: ShoppingBag,
    activeIcon: ShoppingBag,
  },
  {
    label: "Expenses",
    href: `/expenses`,
    Icon: TrendingDown,
    activeIcon: TrendingDown,
  },
  {
    label: "Users",
    href: `/users`,
    Icon: UserIcon,
    activeIcon: UserIcon,
  },
  {
    label: "Customers",
    href: `/customers`,
    Icon: UsersRound,
    activeIcon: UsersRound,
  },
  {
    label: "Suppliers",
    href: `/suppliers`,
    Icon: TruckIcon,
    activeIcon: TruckIcon,
  },
  {
    label: "Credit Management",
    href: `/credit-management`,
    Icon: CreditCardIcon,
    activeIcon: CreditCardIcon,
  },
  {
    label: "Reports",
    href: `/reports`,
    Icon: BarChart3,
    activeIcon: BarChart3,
    subRoutes: [
      {  
       label: "Sales Report",
       href: "/reports/sales",
      },
      {  
       label: "Credit Report",
       href: "/reports/credit",
      },
      {  
       label: "Expense Report",
       href: "/reports/expense",
      },
      {  
       label: "Inventory Stock",
       href: "/reports/inventory",
      },
      {  
       label: "Product Performance",
       href: "/reports/product-performance",
      },
      {  
       label: "Business Performance",
       href: "/reports/business-performance",
      },
      
    ],
  },
  
  {
    label: "Settings",
    Icon: Settings,
    href: `/settings`,
    activeIcon: Settings
  },
];

const orgRoutes = (organizationId: string) => [
  {
    label: "Dashboard",
    href: ``,
    Icon: GoHome,
    activeIcon: GoHomeFill,
  },
  
];

export const Navigation = () => {
  const [currentRoute, setCurrentRoute] = useState("root");
  const [routes, setRoutes] = useState(generalRoutes);
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});
  const pathname = usePathname();

  useEffect(() => {
    const orgPath = pathname.match(/\/organizations\/([^\/]+)/);
    if (orgPath) {
      setRoutes(orgRoutes(orgPath[1]));
      setCurrentRoute("organization");
    } else {
      setRoutes(generalRoutes);
      setCurrentRoute("root");
    }
  }, [pathname]);

  return (
    <ul className="flex flex-col gap-0.5">
      {routes.map((route) => {
        if (route.subRoutes) {
          const isActive = route.subRoutes.some((subRoute) => 
            pathname === subRoute.href || pathname.startsWith(`${subRoute.href}/`)
          );
          const isExpanded = expandedMenus[route.label] || false;
          return (
            <div key={route.label}>
              <button
                onClick={() => setExpandedMenus((prev) => ({ ...prev, [route.label]: !isExpanded }))}
                className={cn(
                  "flex items-center gap-2 p-2 rounded-md font-medium text-sm hover:bg-primary/10 transition w-full text-left",
                  isActive && "bg-primary/10 text-primary",
                  isActive && isExpanded && "bg-primary/10 text-primary"
                )}
              >
                <route.Icon className={cn(
                  "size-4",
                  isActive ? "text-primary" : "text-neutral-500"
                )} />
                <span className="font-semibold">{route.label}</span>
                {isExpanded ? (
                  <ChevronDown className="size-4 ml-auto text-primary" />
                ) : (
                  <ChevronRight className="size-4 ml-auto" />
                )}
              </button>
              {isExpanded && (
                <ul className="pl-8 mt-1 flex flex-col gap-0.5 border-l-2 border-l-primary/20">
                  {route.subRoutes.map((subRoute) => (
                    <Link href={subRoute.href} key={subRoute.href}>
                      <div
                        className={cn(
                          "p-2 rounded-md font-medium text-sm hover:bg-primary/10 transition",
                          (pathname === subRoute.href || pathname.startsWith(`${subRoute.href}/`)) && 
                          "bg-primary/10 text-primary"
                        )}
                      >
                        {subRoute.label}
                      </div>
                    </Link>
                  ))}
                </ul>
              )}
            </div>
          );
        } else {
          const isActive =
            currentRoute === "root"
              ? route.href !== "/"
                ? pathname.startsWith(route.href) || pathname === route.href
                : pathname === route.href
              : pathname === route.href || pathname.startsWith(`${route.href}/`);
          return (
            <Link href={route.href} key={route.href}>
              <div
                className={cn(
                  "flex items-center gap-2 p-2 rounded-md font-medium text-sm hover:bg-primary/10 transition",
                  isActive && "bg-primary/10 text-primary"
                )}
              >
                {isActive ? (
                  <route.activeIcon className="size-4 text-primary" />
                ) : (
                  <route.Icon className="size-4 text-neutral-500" />
                )}
                <span className={cn(isActive && "font-semibold")}>
                  {route.label}
                </span>
              </div>
            </Link>
          );
        }
      })}
    </ul>
  );
};