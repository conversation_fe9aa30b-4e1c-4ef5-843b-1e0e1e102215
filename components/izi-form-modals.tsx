import { CreditPaymentFormModal } from "@/features/credits/components/payments-form-modal";
import { CustomerFormModal } from "@/features/customers/components/customer-form-modal";
import { ExpenseCategoryFormModal } from "@/features/expense_category/components/expense-category-form-modal";
import { ExpenseFormModal } from "@/features/expenses/components/expense-form-modal";
import { SupplierFormModal } from "@/features/suppliers/components/supplier-form-modal";
import { StoreUserFormModal } from "@/features/users/components/user-form-modal";

export default function IziFormModals() {
    return (
        <>
            <CustomerFormModal /> 
            <ExpenseCategoryFormModal />
            <ExpenseFormModal />  
            <StoreUserFormModal />
            <SupplierFormModal />
            <CreditPaymentFormModal />
        </>
    );
} 