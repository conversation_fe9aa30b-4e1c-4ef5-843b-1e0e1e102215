import { LucideIcon, ArrowLef<PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

interface PageHeaderProps {
  title: string;
  description: string;
  icon: LucideIcon;
  className?: string;
  showBackButton?: boolean;
}

export const PageHeader = ({
  title,
  description,
  icon: Icon,
  className,
  showBackButton = true
}: PageHeaderProps) => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <div className={cn("space-y-4 sm:space-y-6", className)}>
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
        <div className="flex-1 min-w-0">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight flex items-center gap-2 sm:gap-3">
            <Icon className="h-5 w-5 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-primary flex-shrink-0" />
            <span className="truncate">{title}</span>
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground mt-1 sm:mt-2 line-clamp-2 sm:line-clamp-none">
            {description}
          </p>
        </div>

        {showBackButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleBack}
            className="flex items-center gap-2 w-full sm:w-auto sm:ml-4 flex-shrink-0"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="sm:inline">Back</span>
          </Button>
        )}
      </div>
    </div>
  );
};
