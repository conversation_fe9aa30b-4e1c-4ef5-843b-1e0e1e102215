"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DottedSeparator } from "@/components/dotted-separator";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
    Form, 
    FormField, 
    FormItem, 
    FormLabel, 
    FormControl, 
    FormMessage 
} from "@/components/ui/form";
import { Select, SelectGroup, SelectTrigger, SelectContent, SelectItem, SelectLabel, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { useEffect } from "react";
import { CreateSupplierSchema } from "../schemas";
import { useCreateSupplier } from "../api/use-create-supplier";
import { useEditSupplier } from "../api/use-edit-supplier";
import { useSupplierFormModal } from "../hooks/use-supplier-form-modal";
import { useRetrieveSupplier } from "../api/use-retrieve-supplier";

interface SupplierFormProps {
    onCancel?: () => void;
    defaultValues?: z.infer<typeof CreateSupplierSchema>;
}


export const SupplierForm = ({ onCancel }: SupplierFormProps) => {
    const organizationId = localStorage.getItem("current_organization") ?? undefined;
    const createSupplier = useCreateSupplier();
    const { close, id } = useSupplierFormModal();
    const { data: supplier } = useRetrieveSupplier(id);
    const editSupplier = useEditSupplier(id);

    const form = useForm<z.infer<typeof CreateSupplierSchema>>({
        resolver: zodResolver(CreateSupplierSchema),
        defaultValues: {
            name: "",
            company: "",
            phone: "",
            address:"",
            organization_id:organizationId || "",
        }
    });

    console.log(supplier?.data)

    useEffect(() => {
        if (id && supplier?.data) {
            const supplierData = supplier.data;

            form.setValue("name", supplierData.name);
            form.setValue("company", supplierData.company);
            form.setValue("phone", supplierData.phone);
            form.setValue("address", supplierData.address);
        }
    }, [supplier?.data, id, form]);

    const onSubmit = async (values: z.infer<typeof CreateSupplierSchema>) => {
        if (id) {
            editSupplier.mutate(
                values , 
                {
                    onSuccess: () => {
                        toast.success("Supplier updated successfully");
                        form.reset();
                        close();
                    },
                }
            );
            return;
        }
        
        createSupplier.mutate(
            values,
            {
                onSuccess: () => {
                    toast.success("Supplier created successfully")
                    form.reset();
                    close();
                },
            }
        );
    };

    return (
        <Card className="w-full h-full border-none shadow-none">
            <CardHeader className="flex p-7">
                <CardTitle className="text-xl font-bold">
                    {id ? "Edit Store User" : "Create Store User"}
                </CardTitle>
            </CardHeader>
            <div className="px-7">
                <DottedSeparator />
            </div>
            <CardContent className="p-7">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} >
                        <div className="flex flex-col gap-y-4">
                            <FormField 
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Full Name</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter full name"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField 
                                control={form.control}
                                name="company"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Company (optional)</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter company" 
                                                type="text"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField 
                                control={form.control}
                                name="phone"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Phone</FormLabel>
                                        <FormControl>
                                            <Input 
                                                placeholder="Enter phone number"
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            
                        </div>
                        <DottedSeparator className="py-7" />
                        <div className="flex items-center justify-between">
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={onCancel}
                                disabled={createSupplier.isPending}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={createSupplier.isPending || editSupplier.isPending}
                            >
                                {id ? "Update Supplier" : "Create Supplier"}
                            </Button>
                        </div>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
};