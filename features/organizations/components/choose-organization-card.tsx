"use client"

import { useState } from "react";
import { Building2, Users, ArrowRight, Plus, MapPin, Phone, Coins, Globe } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useStateManager } from "@/hooks/use-context";
import { useGetOrganizations } from "../api/use-get-organizations";
import { useSelectOrganization } from "../api/use-select-organization";

const mockOrganizations = [
  {
    id: 1,
    name: "Tech Solutions Inc",
    description: "Software development and consulting",
    memberCount: 15,
    logo: null
  },
  {
    id: 2,
    name: "Marketing Agency Pro",
    description: "Digital marketing and brand management",
    memberCount: 8,
    logo: null
  }
];

export const ChooseOrganizationCard = () => {
  const selectOrganization = useSelectOrganization();
  const { isLoading } = useStateManager();

  const handleSelectOrganization = (orgId: number) => {
    selectOrganization.mutate(orgId);
  };

  const organizationsListRaw = typeof window !== "undefined" ? localStorage.getItem("organizations") : null;
  let organizationsList: any[] = [];
  if (organizationsListRaw) {
    try {
      organizationsList = JSON.parse(organizationsListRaw);
    } catch {
      organizationsList = [];
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading organizations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-2xl bg-white/95 backdrop-blur-sm">

        <CardContent className="p-6 md:p-8">
          <div className="grid gap-4 md:gap-6">
            {organizationsList && organizationsList.length > 0 && organizationsList.map((org: any) => (
              <div
                key={org.id}
                onClick={() => handleSelectOrganization(org.id)}
                className={`
                 relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-200
                `}
              >
                <div className="">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Organization Icon */}
                    <div className="w-16 h-16 bg-gradient-to-br from-primary/10 to-primary/20 rounded-xl flex items-center justify-center flex-shrink-0">
                      {org.featured_image && org.featured_image !== "/storage/" ? (
                        <img
                          src={org.featured_image}
                          alt={org.name}
                          className="w-full h-full object-cover rounded-xl"
                        />
                      ) : (
                        <Building2 className="w-8 h-8 text-primary" />
                      )}
                    </div>

                    {/* Organization Details */}
                    <div className="flex-1 min-w-0">
                      {/* Name */}
                      <h3 className="font-bold text-gray-900 text-xl mb-2">
                        {org.name}
                      </h3>

                      {/* Description */}
                      {/* {org.description && (
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {org.description}
                        </p>
                      )} */}

                      {/* Details */}
                      <div className="space-y-3">
                        {/* Location */}
                        {org.address && (
                          <div className="flex items-center text-sm text-gray-600">
                            <MapPin className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                            <span className="truncate">{org.address}</span>
                          </div>
                        )}

                        {/* Members (if available) */}
                        {org.memberCount && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Users className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                            <span>{org.memberCount} members</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center ml-4">
                    <ArrowRight className="w-6 h-6 text-gray-400" />
                  </div>
                </div>

                 {/* Phone and Tokens Row */}
                  <div className="flex items-center gap-6">
                    {/* Phone */}
                    {org.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" />
                        <span>{org.phone}</span>
                      </div>
                    )}

                    {/* Tokens */}
                    <div className="flex items-center text-sm">
                      <Coins className="w-4 h-4 mr-2 text-yellow-500 flex-shrink-0" />
                      <span className="font-medium text-gray-900">
                        {org.tokens?.toLocaleString() || 0} tokens
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      
    </div>
  );
};
