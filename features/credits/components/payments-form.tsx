
"use client"
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
    Form, 
    FormField, 
    FormItem, 
    FormLabel, 
    FormControl, 
    FormMessage 
} from "@/components/ui/form";

import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import { PaymentMethodSelect } from '@/features/credits/components/payment-method-select';
import { Textarea } from '@/components/ui/textarea';
import { Loader2Icon } from "lucide-react";
import { DottedSeparator } from "@/components/dotted-separator";
import { useCreditFormModal } from "../hooks/use-payment-form-modal";
import { useRetrieveCredit } from "../api/use-retrieve-credits";
import { useCreatePayments } from "../api/use-create-payments";
import { paymentFormSchema } from "../schemas";
import { PaymentMethod } from "../types/credits";



interface CreditFormProps {
    onCancel?: () => void;
}


export const CreditForm = ({ onCancel }: CreditFormProps) => {
   
      
     const { close,id } = useCreditFormModal()
     const { data: credit,isLoading} = useRetrieveCredit(id);
     const createPayments = useCreatePayments(id);
     
    const [submitting, setSubmitting] = useState(false);


  
    const form = useForm<z.infer<typeof paymentFormSchema>>({
        resolver: zodResolver(paymentFormSchema),
        defaultValues: {
          amount: 0,
          payment_method: PaymentMethod.CASH,
          notes: '',
        },
    });



useEffect(() => {
  
}, [id]);



const onSubmit = async (values: z.infer<typeof paymentFormSchema>) => {
  if (isLoading) return;
  
  // Validate amount against balance
  if (values.amount > Number(credit?.data?.data.balance)) {
    form.setError('amount', { 
      type: 'manual', 
      message: 'Amount cannot exceed the remaining balance' 
    });
    return;
  }
  
  setSubmitting(true);
  try {
     createPayments.mutate(
      values, 
      {
          onSuccess: () => {
              toast.success("Payment added successfully");
              form.reset();
              close();
          },
         
      }
  );
    
    // Reset form and reload details
    form.reset({
      amount: 0,
      payment_method:PaymentMethod.CASH,
      notes: '',
    });
    
    
  } catch (error) {
    console.log("Error:", error);
  
  } finally {
    setSubmitting(false);
  }
};

    
   


    return (
        <Card className="w-full h-full border-none shadow-none">
        <CardHeader>
          <CardTitle>Add Payment</CardTitle>
          <CardDescription>Record a payment against this credit</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
               
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Enter payment amount"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="payment_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Method</FormLabel>
                    <FormControl>
                      <PaymentMethodSelect
                        value={field.value}
                        onValueChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any notes about this payment"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            <DottedSeparator className="py-7" />
           <div className="flex items-center justify-between">
            <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            disabled={submitting}
             >
             Cancel
            </Button>
             <Button 
                type="submit" 
                disabled={submitting}
              >
                {submitting && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
                Record Payment
              </Button>
                                          
             </div>
              
             
            </form>
          </Form>
        </CardContent>
      </Card>
    );
};