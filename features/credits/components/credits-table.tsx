import { DataTable } from "@/components/data-table";
import React, { useEffect, useState } from "react";
import { Row } from "@tanstack/react-table";
import { creditColumns } from "../columns";
import { useFetchCredits } from "../api/use-fetch-credits";
import { Credits, FetchCreditRequest, FetchCreditResponse } from "../types/credits";

interface CreditTableProps {
  onRowSelectedChanged?: (rows: Row<Credits>[]) => void;
  onDataChange?: (data?: FetchCreditResponse | undefined) => void;
  filters?: FetchCreditRequest;
  organization_id: string;
}

export const CreditDataTable: React.FC<CreditTableProps> = ({
  onRowSelectedChanged,
  onDataChange,
  filters = {},
  organization_id,
}) => {
  const [request, setRequest] = useState<FetchCreditRequest>(filters);
  const { data: credits , isLoading } = useFetchCredits(request);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = useState<Row<Credits>[]>([]);
  
  useEffect(() => {
    onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, onRowSelectedChanged]);

  useEffect(() => {
    onDataChange?.(credits);
  }, [credits, onDataChange]);

  useEffect(() => {
    setRequest((prev) => ({
      ...prev,
      ...filters,
    }));
  }, [filters]);

  

  return (
    <DataTable
    columns={creditColumns(organization_id)}
    data={credits?.data ?? []}
    manualPagination={true} 
    total={credits?.total} 
    onPaginationChange={(pg) => {
      if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
        setPagination(pg);
        setRequest((prev) => ({
          ...prev,
          page: (pg.pageIndex + 1).toString(), 
        }));
      }
    }}
    onRowSelectedChanged={setRowSelection}
    isLoading={isLoading}
  />
  );
};