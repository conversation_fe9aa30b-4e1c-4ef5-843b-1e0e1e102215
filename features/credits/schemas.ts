import { z } from "zod";
import { PaymentMethod } from "./types/credits";
export const fetchCreditSchema = z.object({
    query: z.string().optional(),
    organization_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    per_page: z.string().optional(),
    total:z.string().optional()
});


export const paymentFormSchema = z.object({
    amount: z.number(),
    payment_method: z.nativeEnum(PaymentMethod),
    notes: z.string().optional(),
  });


export const AgingSchema = z.object({
    organization_id: z.string().optional(),
  
});
  
  