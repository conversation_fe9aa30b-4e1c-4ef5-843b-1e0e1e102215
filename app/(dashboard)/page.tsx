"use client";
import { Package, Users, DollarSign, ArrowUpRight, RefreshCw, LayoutDashboard } from "lucide-react";
import { PageHeader } from "@/components/page-header";
import { useStateManager } from "@/hooks/use-context";
import { TokenDisplay } from "@/features/dashboard/components/token-display";
import { SalesDisplay } from "@/features/dashboard/components/sales-display";
import { StatsCard } from "@/features/dashboard/components/stats-card";
import { QuickActions } from "@/features/dashboard/components/quick-actions";
import { MobileFAB } from "@/components/mobile-fab";

// Mock dashboard stats
const mockStats = {
  todaySales: 12.89,
  totalSales: 45678.90,
  totalCustomers: 156,
  totalProducts: 89,
  totalOrders: 234,
  lowStockItems: 5,
  recentSales: [],
  topProducts: [],
  salesTrend: []
};

const OrganizationDashboard = () => {
  const organizationIdString = localStorage.getItem("current_organization");
  const organizationId = organizationIdString ? Number(organizationIdString) : undefined;
  const { currentOrganization } = useStateManager();

  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard Overview"
        description={`Welcome back, ${currentOrganization?.name || "Admin"}! Here's your business overview.`}
        icon={LayoutDashboard}
      />

      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 -mx-6 -mt-6 pt-6 px-6">
        <div className="max-w-7xl mx-auto space-y-6">

        {/* Featured Sales & Tokens Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <SalesDisplay
              todaySales={mockStats.todaySales}
              totalSales={mockStats.totalSales}
              trend={{
                value: 15.2,
                isPositive: true
              }}
            />
          </div>
          <div className="lg:col-span-1">
            <TokenDisplay
              tokens={(currentOrganization as any).tokens?.toLocaleString() || 0}
              onAddTokens={() => console.log("Add tokens clicked")}
            />
          </div>
        </div>

        {/* Business Metrics Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <StatsCard
            title="Monthly Revenue"
            value={`TSH ${mockStats.totalSales.toLocaleString()}/=`}
            icon={DollarSign}
            description={
              <span className="flex items-center text-green-600">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                12% from last month
              </span>
            }
            className="bg-white border border-gray-200 hover:shadow-lg transition-all duration-300 rounded-xl"
            valueClassName="text-2xl lg:text-3xl font-bold text-gray-900"
            iconContainerClassName="bg-green-100 text-green-600"
          />
          <StatsCard
            title="Total Users"
            value={mockStats.totalCustomers}
            icon={Users}
            description="+8 new this week"
            className="bg-white border border-gray-200 hover:shadow-lg transition-all duration-300 rounded-xl"
            valueClassName="text-2xl lg:text-3xl font-bold text-gray-900"
          />
          <StatsCard
            title="Products in Stock"
            value={mockStats.totalProducts}
            icon={Package}
            description={`${mockStats.lowStockItems} low stock items`}
            className="bg-white border border-gray-200 hover:shadow-lg transition-all duration-300 rounded-xl"
            valueClassName="text-2xl lg:text-3xl font-bold text-gray-900"
            iconContainerClassName="bg-purple-100 text-purple-600"
          />
        </div>

        {/* Quick Actions Section */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
              <p className="text-gray-500 text-sm">Get things done faster with one-click shortcuts</p>
            </div>
            <button className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
              View all <ArrowUpRight className="h-4 w-4" />
            </button>
          </div>
          {organizationId !== undefined && (
            <QuickActions organizationId={organizationId} />
          )}
        </div>

        {/* Mobile Floating Action Button */}
        <MobileFAB />
        </div>
      </div>
    </div>
  );
};

export default OrganizationDashboard;