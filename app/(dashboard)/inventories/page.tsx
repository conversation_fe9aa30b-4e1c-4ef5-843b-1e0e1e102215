"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Inventory Items imports
import { InventoryItemTable } from "@/features/inventories/components/inventory-table";
import { FetchInventoryRequest, FetchInventoryResponse, InventoryItem } from "@/features/inventories/types/inventories";

// Inventory Categories imports
import { InventoryCategoryTable } from "@/features/inventory_category/components/inventory-categories-table";
import { FetchInventoryCategoryRequest, FetchInventoryCategoryResponse, InventoryCategory } from "@/features/inventory_category/types/inventoryCategories";

import { Row } from "@tanstack/react-table";
import { Download, Filter, Plus, Trash, Package, FolderOpen, Warehouse } from "lucide-react";
import { useEffect } from "react";
import { FaFileExcel } from "react-icons/fa";
import { PageHeader } from "@/components/page-header";

export default function InventoriesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get tab from URL or default to "inventories"
  const currentTab = searchParams.get("tab") || "inventories";

  // Inventory Items state
  const [selectedInventoryRows, setSelectedInventoryRows] = useState<Row<InventoryItem>[]>([]);
  const [inventoryResponse, setInventoryResponse] = useState<FetchInventoryResponse | null>(null);
  const [inventorySearch, setInventorySearch] = useState<string>("");
  const [inventoryFilters, setInventoryFilters] = useState<FetchInventoryRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });

  // Inventory Categories state
  const [selectedCategoryRows, setSelectedCategoryRows] = useState<Row<InventoryCategory>[]>([]);
  const [categoryResponse, setCategoryResponse] = useState<FetchInventoryCategoryResponse | null>(null);
  const [categorySearch, setCategorySearch] = useState<string>("");
  const [categoryFilters, setCategoryFilters] = useState<FetchInventoryCategoryRequest>({
    organization_id: localStorage.getItem("current_organization")!,
  });

  // Handle tab change
  const handleTabChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("tab", value);
    router.push(`/inventories?${params.toString()}`);
  };

  // Update filters when search changes
  useEffect(() => {
    setInventoryFilters((prev) => ({
      ...prev,
      query: inventorySearch,
    }));
  }, [inventorySearch]);

  useEffect(() => {
    setCategoryFilters((prev) => ({
      ...prev,
      query: categorySearch,
    }));
  }, [categorySearch]);

  return (
    <div className="space-y-6">
      <PageHeader
        title="Inventory Management"
        description="Manage your inventory items and categories"
        icon={Warehouse}
      />

      <Tabs value={currentTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="inventories" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Inventory Items
          </TabsTrigger>
          <TabsTrigger value="categories" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Categories
          </TabsTrigger>
        </TabsList>

        {/* Inventory Items Tab */}
        <TabsContent value="inventories" className="space-y-4">
          <Card>
            <CardHeader className="gap-y-2 flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Inventory Items
                </CardTitle>
                <CardDescription className="text-sm">
                  {inventoryResponse?.total ?? 0} items in total
                </CardDescription>
              </div>

              <div className="flex items-center gap-x-2">
                <Button
                  onClick={() => {}}
                  variant="destructive"
                  size="sm"
                  className="mt-1"
                >
                  <Download className="size-4 mr-2" />
                  Export Inventory
                </Button>

                <Button
                  onClick={() => {}}
                  variant="outline"
                  size="sm"
                  className="mt-1"
                >
                  <FaFileExcel className="size-4 mr-2" />
                  Import Inventory
                </Button>
                <Button
                  onClick={() => {}}
                  size="sm"
                  className="mt-1"
                >
                  <Plus className="size-4 mr-2" />
                  Add New Item
                </Button>
              </div>
            </CardHeader>

            <CardContent>
              <div className="mb-2 flex justify-start items-stretch gap-x-2">
                <input
                  className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
                  placeholder="Search inventory items..."
                  value={inventorySearch}
                  onChange={(e) => setInventorySearch(e.target.value)}
                />
                <div className="flex-1"></div>

                <Button
                  size="sm"
                  variant="destructive"
                  disabled={selectedInventoryRows.length == 0}
                >
                  <Trash /> Bulk Delete{" "}
                  {selectedInventoryRows.length > 0 && (
                    <Badge variant="secondary">{selectedInventoryRows.length}</Badge>
                  )}
                </Button>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button size="sm" variant="secondary">
                      <Filter />
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent side="left" className="w-80">
                    Filter
                  </PopoverContent>
                </Popover>
              </div>
              <InventoryItemTable
                onRowSelectedChanged={setSelectedInventoryRows}
                onDataChange={(res) => res && setInventoryResponse(res)}
                filters={inventoryFilters}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Inventory Categories Tab */}
        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader className="gap-y-2 flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FolderOpen className="h-5 w-5" />
                  Inventory Categories
                </CardTitle>
                <CardDescription className="text-sm">
                  {categoryResponse?.total ?? 0} categories in total
                </CardDescription>
              </div>
              <Button
                onClick={() => {}}
                size="sm"
                className="mt-4"
              >
                <Plus className="size-4 mr-2" />
                Add New Category
              </Button>
            </CardHeader>

            <CardContent>
              <div className="mb-2 flex justify-start items-stretch gap-x-2">
                <input
                  className="border focus:outline-none px-2 py-1 text-xs rounded-lg w-80"
                  placeholder="Search categories..."
                  value={categorySearch}
                  onChange={(e) => setCategorySearch(e.target.value)}
                />
                <div className="flex-1"></div>

                <Button
                  size="sm"
                  variant="destructive"
                  disabled={selectedCategoryRows.length == 0}
                >
                  <Trash /> Bulk Delete{" "}
                  {selectedCategoryRows.length > 0 && (
                    <Badge variant="secondary">{selectedCategoryRows.length}</Badge>
                  )}
                </Button>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button size="sm" variant="secondary">
                      <Filter />
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent side="left" className="w-80">
                    Filter
                  </PopoverContent>
                </Popover>
              </div>
              <InventoryCategoryTable
                onRowSelectedChanged={setSelectedCategoryRows}
                onDataChange={(res) => res && setCategoryResponse(res)}
                filters={categoryFilters}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
