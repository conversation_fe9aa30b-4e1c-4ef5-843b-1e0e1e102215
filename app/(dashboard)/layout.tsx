
"use client"

import React from "react";

import "../globals.css";
import { Sidebar } from "@/components/sidebar";
import { Navbar } from "@/components/navbar";
import { useStateManager } from "@/hooks/use-context";
import { redirect } from "next/navigation";
import { NuqsAdapter } from 'nuqs/adapters/next/app'
import IziFormModals from "@/components/izi-form-modals";


interface DashboardLayoutProps {
    children?: React.ReactNode
}

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
    const { isLoading, isAuthenticated } = useStateManager();
    
    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading...</p>
                </div>
            </div>
        );
    }

    // Redirect to auth if not authenticated
    if (!isAuthenticated) {
        redirect("/sign-in");
    }

   

    return (
        <div className="min-h-screen">
            <div className="flex w-full h-full">
                <div className="fixed left-0 top-0 hidden lg:block lg:w-[264px] h-full overflow-y-auto">
                    <Sidebar /> 
                </div>  
                <div className="lg:pl-[264px] w-full">
                    <div className="mx-auto max-w-screen-2xl h-full">
                        <Navbar />
                        <main className="h-full py-8 px-6 mt-5 sm:mt-1 flex flex-col">
                           
                            <NuqsAdapter>
                                {children}
                                <IziFormModals />
                            </NuqsAdapter>

                        </main>
                    </div>
                </div>
            </div>
        </div>
    )
};

export default DashboardLayout;
